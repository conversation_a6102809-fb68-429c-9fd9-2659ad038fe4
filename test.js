const robot = require("robotjs");

function getRandomInterval(min = 3000, max = 7000) {
  return Math.floor(Math.random() * (max - min + 1)) + min;
}

function getRandomPosition() {
  const screenSize = robot.getScreenSize();
  const x = Math.floor(Math.random() * (screenSize.width - 100)) + 50; // Évite les bords extrêmes
  const y = Math.floor(Math.random() * (screenSize.height - 100)) + 50;
  return { x, y };
}

function moveMouseNaturally() {
  const { x, y } = getRandomPosition();
  const speed = Math.random() * 3 + 1; // Variation de la vitesse pour plus de naturel

  robot.moveMouseSmooth(x, y, speed);

  setTimeout(() => {
    if (Math.random() > 0.65) {
      scrollInVSCode();
    }

    if (Math.random() > 0.75) {
      changeFileInVSCode();
    }

    if (Math.random() > 0.85) {
      switchVSCodeTab();
    }

    setTimeout(moveMouseNaturally, getRandomInterval());
  }, getRandomInterval(1000, 3000)); // Petite pause après le déplacement
}

function scrollInVSCode() {
  const scrollAmount = Math.random() > 0.5 ? 3 : -3;
  robot.scrollMouse(0, scrollAmount * 10);
}

function changeFileInVSCode() {
  const direction = Math.random() > 0.5 ? "right" : "left";
  robot.keyTap(direction, "control");
}

function switchVSCodeTab() {
  robot.keyTap("tab", "control");
}

// Lancer l'automatisation naturelle
moveMouseNaturally();
